// nicknameScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { setState } from '../../msec/framework/state.js';
import { navigate } from '../../msec/framework/router.js';
import { send, disableAutoHandshake, enableAutoHandshake, connect, isConnected } from '../multiplayer/socket.js';

export function nicknameScreen() {
  const root = document.getElementById('app');

  // Disable auto-handshake when user explicitly goes to nickname screen
  // This allows them to enter a new nickname
  disableAutoHandshake();
  
  // Always ensure a fresh WebSocket connection for new rounds
  console.log('[NicknameScreen] Ensuring fresh WebSocket connection for new round');
  const wsHost = window.location.hostname === 'localhost' ? 'localhost' : window.location.hostname;
  const wsUrl = `ws://${wsHost}:8080`;
  console.log('[NicknameScreen] Establishing fresh WebSocket connection:', wsUrl);
  connect(wsUrl);
  const vdom = {
    tag: 'div',
    attrs: { class: 'nickname-form' },
    children: [
      {tag: 'div', attrs: { class: 'character-container' }, children: [
        {tag: 'img', attrs: { src: '../static/images/character_header.png', alt: 'Bomberman Logo', class: 'img-header' }, children: []},
      ]},
      {tag: 'div', attrs: { class: 'nickname-container' }, children: [
      {tag: 'div', attrs: { class: 'nickname-form' }, children: [
      { tag: 'h2', attrs: {}, children: ['Enter your nickname'] },
      {
        tag: 'form',
        attrs: { id: 'nickname-form', autocomplete: 'off' },
        children: [
          {
            tag: 'input',
            attrs: {
              type: 'text',
              id: 'nickname-input',
              placeholder: 'Nickname',
              maxlength: 16,
              required: true,
              autofocus: true,
            },
            children: [],
          },
          {
            tag: 'button',
            attrs: { type: 'submit' },
            children: ['Join'],
          },
        ],
      },
      {
        tag: 'div',
        attrs: { class: 'links-demo', style: 'margin-top: 50px; font-size: 14px;' },
        children: [
          // { tag: 'p', attrs: {}, children: ['Test links:'] },
          {
            tag: 'a',
            attrs: { href: '/lobby', style: 'margin-right: 10px;' },
            children: ['Go to Lobby (Internal)']
          },
          {
            tag: 'a',
            attrs: { href: 'https://github.com', style: 'margin-right: 10px;' },
            children: ['GitHub (External)']
          },
          {
            tag: 'a',
            attrs: { href: 'https://developer.mozilla.org'},
            children: ['MDN (External)']
          },
        ],
      },
      ]},
    ]},
  ]
  };
  renderDOM(vdom, root);

  // Pre-populate nickname input if we have one stored
  const storedNickname = localStorage.getItem('nickname');
  if (storedNickname) {
    const nicknameInput = document.getElementById('nickname-input');
    if (nicknameInput) {
      nicknameInput.value = storedNickname;
    }
  }

  document.getElementById('nickname-form').onsubmit = (e) => {
    e.preventDefault();
    const nickname = document.getElementById('nickname-input').value.trim();
    if (!nickname) return;

    console.log('[NicknameScreen] New nickname submitted:', nickname);

    // Persist nickname and create session timestamp
    localStorage.setItem('nickname', nickname);
    localStorage.setItem('sessionTimestamp', Date.now().toString());

    // Re-enable auto-handshake for future reconnects
    enableAutoHandshake();

    // Ensure fresh WebSocket connection for new nickname
    const wsHost = window.location.hostname === 'localhost' ? 'localhost' : window.location.hostname;
    const wsUrl = `ws://${wsHost}:8080`;
    console.log('[NicknameScreen] Establishing fresh WebSocket connection:', wsUrl);
    connect(wsUrl);

    // Wait for connection to be established before sending nickname
    let connectionAttempts = 0;
    const maxAttempts = 50; // 5 seconds max wait
    
    const checkConnection = () => {
      connectionAttempts++;
      if (isConnected()) {
        console.log('[NicknameScreen] WebSocket connected, sending nickname');
        send({ type: 'nickname', nickname });
        navigate('/lobby');
      } else if (connectionAttempts < maxAttempts) {
        console.log('[NicknameScreen] WebSocket still connecting, waiting... (attempt', connectionAttempts, '/', maxAttempts, ')');
        setTimeout(checkConnection, 100);
      } else {
        console.error('[NicknameScreen] WebSocket connection timeout, navigating anyway');
        // Navigate anyway, connection will be established in lobby
        navigate('/lobby');
      }
    };
    
    // Start checking connection status
    setTimeout(checkConnection, 100);
  };
}
