// gameEndScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { sendAction } from '../multiplayer/sync.js';
import { navigate } from '../../msec/framework/router.js';
import { startNewSession, enableAutoHandshake } from '../multiplayer/socket.js';

export function gameEndScreen(root, winner, players) {
  console.log('Rendering GameEndScreen for winner:', winner);
  
  // Determine winner message
  let winnerMessage = '';
  let winnerColor = '#F6265A';
  
  if (winner === 'Draw') {
    winnerMessage = 'GAME ENDED IN A DRAW!';
    winnerColor = '#FFA500';
  } else {
    winnerMessage = `${winner.toUpperCase()} WINS!`;
  }

  // Create final scores
  const finalScores = players
    .sort((a, b) => {
      // Sort by alive status first, then by lives
      if (a.alive && !b.alive) return -1;
      if (!a.alive && b.alive) return 1;
      return b.lives - a.lives;
    })
    .map((player, index) => ({
      tag: 'div',
      attrs: { 
        style: `
          display: flex; 
          justify-content: space-between; 
          align-items: center;
          padding: 10px 20px; 
          margin: 5px 0; 
          background: ${player.alive ? 'rgba(0, 255, 0, 0.1)' : 'rgba(255, 0, 0, 0.1)'}; 
          border-radius: 8px;
          border-left: 4px solid ${player.alive ? '#00FF00' : '#FF0000'};
        `
      },
      children: [
        {
          tag: 'span',
          attrs: { style: 'font-weight: bold; color: #333;' },
          children: [`${index + 1}. ${player.nickname}`]
        },
        {
          tag: 'span',
          attrs: { style: 'color: #666;' },
          children: [player.alive ? `${player.lives} lives` : 'Eliminated']
        }
      ]
    }));

  const vdom = {
    tag: 'div',
    attrs: { 
      class: 'game-end-container',
      style: `
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #07131F 0%, #1a1a2e 100%);
        color: white;
        font-family: 'EngraversGothic', Arial, sans-serif;
        padding: 20px;
      `
    },
    children: [
      {
        tag: 'div',
        attrs: { 
          class: 'game-end-screen',
          style: `
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          `
        },
        children: [
          {
            tag: 'h1',
            attrs: { 
              style: `
                color: ${winnerColor};
                font-size: 3rem;
                margin-bottom: 30px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
                letter-spacing: 4px;
              `
            },
            children: [winnerMessage]
          },
          {
            tag: 'div',
            attrs: { 
              class: 'final-scores',
              style: `
                background: rgba(255, 255, 255, 0.05);
                border-radius: 15px;
                padding: 20px;
                margin: 30px 0;
              `
            },
            children: [
              {
                tag: 'h3',
                attrs: { 
                  style: `
                    color: #FFF;
                    margin-bottom: 20px;
                    font-size: 1.5rem;
                    letter-spacing: 2px;
                  `
                },
                children: ['FINAL SCORES']
              },
              ...finalScores
            ]
          },
          {
            tag: 'div',
            attrs: { 
              class: 'game-end-actions',
              style: `
                display: flex;
                justify-content: center;
                margin-top: 30px;
              `
            },
            children: [
              {
                tag: 'button',
                attrs: { 
                  id: 'return-lobby-btn',
                  style: `
                    background: linear-gradient(45deg, #F6265A, #FF4081);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 10px;
                    font-size: 1.1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    letter-spacing: 1px;
                    text-transform: uppercase;
                    font-family: 'EngraversGothic', Arial, sans-serif;
                  `
                },
                children: ['Return to Main Menu']
              }
            ]
          }
        ]
      }
    ]
  };

  renderDOM(vdom, root);

  // Add event listeners
  const lobbyBtn = document.getElementById('return-lobby-btn');

  if (lobbyBtn) {
    lobbyBtn.addEventListener('click', () => {
      console.log('[GameEndScreen] Return to Main Menu button clicked');
      
      // Send mainMenu action instead of lobby action
      // This ensures only this player is affected, not the entire game
      sendAction({ type: 'mainMenu' });
      console.log('[GameEndScreen] Sent mainMenu action to server');

      // Clear session
      startNewSession();
      console.log('[GameEndScreen] Cleared session');
      
      // AGGRESSIVE navigation to main page - bypass everything
      console.log('[GameEndScreen] AGGRESSIVELY navigating to main page');
      
      // Use the most direct method possible
      window.location.href = window.location.origin + '/';
      
      // If that doesn't work, try again
      setTimeout(() => {
        if (window.location.pathname !== '/') {
          console.log('[GameEndScreen] First attempt failed, trying again');
          window.location.replace(window.location.origin + '/');
        }
      }, 100);
    });

    lobbyBtn.addEventListener('mouseenter', () => {
      lobbyBtn.style.transform = 'translateY(-2px)';
      lobbyBtn.style.boxShadow = '0 8px 20px rgba(246, 38, 90, 0.4)';
    });

    lobbyBtn.addEventListener('mouseleave', () => {
      lobbyBtn.style.transform = 'translateY(0)';
      lobbyBtn.style.boxShadow = 'none';
    });
  }
}
