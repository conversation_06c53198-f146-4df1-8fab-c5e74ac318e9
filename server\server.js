// server/server.js

const WebSocket = require('ws');
const GameState = require('./gameState.js');
const { v4: uuidv4 } = require('uuid');

const PORT = 8080;
const wss = new WebSocket.Server({ port: PORT });
const game = new GameState();

// Map of playerId -> ws
const clients = new Map();
// Map of ws -> playerId
const wsToId = new Map();
const playerConnections = new Map(); // playerId -> Set of ws
const pendingRemovals = new Map(); // playerId -> timeoutId

console.log(`WebSocket server running on ws://localhost:${PORT}`);

wss.on('connection', (ws) => {
  let playerId = null;
  let nickname = null;

  ws.on('message', (data) => {
    let msg;
    try {
      msg = JSON.parse(data);
    } catch (e) {
      return;
    }
    // First message must be nickname (optionally with playerId)
    if (!playerId && msg.type === 'nickname') {
      nickname = (msg.nickname || '').trim().slice(0, 16);
      if (!nickname) return;
      
      // If the game is in 'end' phase, force it back to lobby to allow new players
      if (game.phase === 'end') {
        console.log('[Server] Game was in end phase, forcing return to lobby for new player');
        game.returnToLobby();
      }
      
      // Cancel any pending removal for this player BEFORE addPlayer
      if (msg.playerId && pendingRemovals.has(msg.playerId)) {
        clearTimeout(pendingRemovals.get(msg.playerId));
        pendingRemovals.delete(msg.playerId);
      }
      // Try to reuse playerId if provided and not already taken
      let player = null;
      if (msg.playerId) {
        player = game.players.find(p => p.id === msg.playerId);
        if (player) {
          player.alive = true;
          player.nickname = nickname;
        }
      }
      if (!player) {
        player = game.addPlayer(nickname);
      }
      if (!player) {
        ws.send(JSON.stringify({ type: 'error', error: 'Game full' }));
        ws.close();
        return;
      }
      playerId = player.id;
      clients.set(playerId, ws);
      wsToId.set(ws, playerId);
      // Track this connection for the player
      if (!playerConnections.has(playerId)) playerConnections.set(playerId, new Set());
      playerConnections.get(playerId).add(ws);
      // Send playerId to client
      ws.send(JSON.stringify({ type: 'playerId', id: playerId }));
      return;
    }
    if (!playerId) return;
    // Handle actions
    if (msg.type === 'action') {
      game.handleAction(playerId, msg.action);
      
      // If the action is mainMenu, handle it specially
      if (msg.action.type === 'mainMenu') {
        console.log('[Server] Player', playerId, 'requested main menu');
        
        // Check if game phase changed to 'end' (meaning all players see end screen)
        if (game.phase === 'end') {
          console.log('[Server] Game ended for all players - player', playerId, 'will see end screen first');
          
          // Don't close the connection immediately - let the player see the end screen
          // The gameState will handle player removal after a delay
          return;
        } else {
          // Only this player was removed, close their connection
          console.log('[Server] Only player', playerId, 'removed - closing their connection');
          ws.close();
          return;
        }
      }
    } else if (msg.type === 'chat') {
      game.handleChat(playerId, msg.text);
    }
  });

  ws.on('close', () => {
    if (playerId) {
      console.log('[Server] Player', playerId, 'disconnected');
      
      // Remove this connection from the player's set
      const conns = playerConnections.get(playerId);
      if (conns) {
        conns.delete(ws);
        if (conns.size === 0) {
          // Delay removal to allow for quick reconnects
          const timeoutId = setTimeout(() => {
            console.log('[Server] Removing player', playerId, 'after disconnect timeout');
            game.removePlayer(playerId);
            playerConnections.delete(playerId);
            pendingRemovals.delete(playerId);
          }, 2000);
          pendingRemovals.set(playerId, timeoutId);
        }
      }
      clients.delete(playerId);
      wsToId.delete(ws);
    }
  });
});

// Game loop: update and broadcast state
setInterval(() => {
  const oldPhase = game.phase;
  const oldWinner = game.winner;
  
  game.update();
  
  const state = game.getState();
  
  // Only log when state actually changes
  if (oldPhase !== game.phase || oldWinner !== game.winner) {
    console.log('[Server] Game state changed - Phase:', oldPhase, '->', game.phase, 'Winner:', oldWinner, '->', game.winner);
  }
  
  const msg = JSON.stringify({ type: 'state', state });
  
  for (const ws of wss.clients) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(msg);
    }
  }
}, 1000 / 30); // 30 FPS broadcast
