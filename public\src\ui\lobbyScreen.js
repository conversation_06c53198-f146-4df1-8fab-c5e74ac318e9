// lobbyScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { getState, setState } from '../../msec/framework/state.js';
import { navigate } from '../../msec/framework/router.js';
import { subscribeToState, sendChat } from '../multiplayer/sync.js';
import { enableAutoHandshake, closeConnection, connect, isConnected } from '../multiplayer/socket.js';

let lastPlayerList = '';
let lastCount = 0;
let lastPhase = '';
let lastCountdown = 0;
let lastWaitingTime = 0;
let lastChatStr = '';

function updateChatMessages(chatMessages) {
  const chatContainer = document.querySelector('.chat-container-inner');
  if (chatContainer) {
    if (chatMessages && chatMessages.length > 0) {
      const messages = chatMessages.slice(-10).map(msg => `
        <div class="chat-message">
          <span class="player-font">${msg.nickname}:</span>
          <p>${msg.text}</p>
        </div>
      `).join('');
      chatContainer.innerHTML = messages;
    } else {
      chatContainer.innerHTML = `
        <div class="chat-message" style="opacity: 0.6; font-style: italic;">
          <p>No messages yet. Start the conversation!</p>
        </div>
      `;
    }
  }
}

function updateTimerDisplay(countdown, waitingTime) {
  // Find the timer display element and update it without re-rendering the whole page
  const waitingElement = document.querySelector('.lobby-waiting');
  const countdownElement = document.querySelector('.lobby-countdown');
  
  if (waitingTime > 0) {
    // Show waiting time
    if (waitingElement) {
      waitingElement.textContent = `Waiting for more players: ${waitingTime}s`;
      waitingElement.style.display = 'block';
    }
    if (countdownElement) {
      countdownElement.style.display = 'none';
    }
  } else if (countdown > 0) {
    // Show countdown
    if (countdownElement) {
      countdownElement.textContent = `Game starts in: ${countdown}s`;
      countdownElement.style.display = 'block';
    }
    if (waitingElement) {
      waitingElement.style.display = 'none';
    }
  } else {
    // Both timers are 0, hide both
    if (waitingElement) {
      waitingElement.style.display = 'none';
    }
    if (countdownElement) {
      countdownElement.style.display = 'none';
    }
  }
}

export function lobbyScreen() {
  const root = document.getElementById('app');
  let started = false;

  // Re-enable auto-handshake when in lobby (for reconnects)
  enableAutoHandshake();
  
  // Ensure WebSocket connection exists for lobby
  if (!isConnected()) {
    console.log('[LobbyScreen] No WebSocket connection found, establishing one');
    const wsHost = window.location.hostname === 'localhost' ? 'localhost' : window.location.hostname;
    const wsUrl = `ws://${wsHost}:8080`;
    connect(wsUrl);
    
    // Wait a bit for connection to establish before proceeding
    setTimeout(() => {
      if (isConnected()) {
        console.log('[LobbyScreen] WebSocket connection established successfully');
      } else {
        console.warn('[LobbyScreen] WebSocket connection still not ready');
      }
    }, 500);
  } else {
    console.log('[LobbyScreen] WebSocket connection already exists');
  }

  function renderLobby(players, count, countdown, waitingTime, chatMessages) {
    const vdom = {
      tag: 'div',
      attrs: { class: 'lobby-container' },
      children: [
        {tag: 'div', attrs: { class: 'lobby-screen' }, children: [
          { tag: 'h2', attrs: {}, children: ['Lobby'] },
          { tag: 'div', attrs: {}, children: [`Players: ${count}/4`] },
          {
            tag: 'ul',
            attrs: { class: 'player-list' },
            children: players.map(p => ({
              tag: 'li',
              attrs: {},
              children: [p.nickname],
            })),
          },
          // Show waiting time or countdown
          { tag: 'div', attrs: { class: 'lobby-waiting', style: waitingTime > 0 ? 'display: block' : 'display: none' }, children: [`Waiting for more players: ${waitingTime}s`] },
          { tag: 'div', attrs: { class: 'lobby-countdown', style: countdown > 0 ? 'display: block' : 'display: none' }, children: [`Game starts in: ${countdown}s`] },

          // Chat section
          // {
          //   tag: 'div',
          //   attrs: { class: 'lobby-chat', style: 'margin-top: 20px; border: 1px solid #666; border-radius: 8px; padding: 10px; max-height: 200px; overflow-y: auto;' },
          //   children: [
          //     { tag: 'h4', attrs: { style: 'margin: 0 0 10px 0; color: #ccc;' }, children: ['Chat'] },
          //     {
          //       tag: 'div',
          //       attrs: { class: 'chat-messages', id: 'lobby-chat-messages', style: 'min-height: 100px; max-height: 120px; overflow-y: auto; margin-bottom: 10px; padding: 5px; background: #333; border-radius: 4px;' },
          //       children: (chatMessages || []).slice(-8).map(msg => ({
          //         tag: 'div',
          //         attrs: { style: 'margin-bottom: 5px; font-size: 12px;' },
          //         children: [
          //           {
          //             tag: 'span',
          //             attrs: { style: 'color: #4af; font-weight: bold;' },
          //             children: [`${msg.nickname}: `]
          //           },
          //           {
          //             tag: 'span',
          //             attrs: { style: 'color: #fff;' },
          //             children: [msg.text]
          //           }
          //         ]
          //       }))
          //     },
          //     {
          //       tag: 'form',
          //       attrs: { id: 'lobby-chat-form', style: 'display: flex; gap: 5px;' },
          //       children: [
          //         {
          //           tag: 'input',
          //           attrs: {
          //             type: 'text',
          //             id: 'lobby-chat-input',
          //             placeholder: 'Type a message...',
          //             style: 'flex: 1; padding: 5px; border: 1px solid #666; border-radius: 4px; background: #222; color: #fff;',
          //             maxlength: '100'
          //           }
          //         },
          //         {
          //           tag: 'button',
          //           attrs: {
          //             type: 'submit',
          //             style: 'padding: 5px 10px; border: 1px solid #666; border-radius: 4px; background: #4af; color: #fff; cursor: pointer;'
          //           },
          //           children: ['Send']
          //         }
          //       ]
          //     }
          //   ]
          // },
          {
            tag: 'div',
            attrs: { class: 'links-demo', style: 'margin-top: 20px; font-size: 14px;' },
            children: [
              {
                tag: 'a',
                attrs: { href: '/', style: 'color: #4af; margin-right: 10px;' },
                children: ['Back to Main Menu']
              },
              {
                tag: 'a',
                attrs: { href: '/game', style: 'color: #4af; margin-right: 10px;' },
                children: ['Go to Game (Internal)']
              },
            ],
          },
        ]},
        // new chat
            {
              tag: 'div',
              attrs: { class: 'chat-container' },
              children: [
                {
                  tag: 'div',
                  attrs: { class: 'chat-drawer' },
                  children: [
                    { tag: 'p', attrs: { class: 'player-font' }, children: ['Chat'] }
                  ]
                },
                {
                tag: 'div',
                attrs: { class: 'chat-sidebar' },
                children: [
                  {
                    tag: 'div',
                    attrs: { class: 'chat-messages' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'chat-container-inner' },
                        children: (chatMessages && chatMessages.length > 0)
                          ? chatMessages.slice(-10).map(msg => ({
                              tag: 'div',
                              attrs: { class: 'chat-message' },
                              children: [
                                { tag: 'span', attrs: { class: 'player-font' }, children: [`${msg.nickname}:`] },
                                { tag: 'p', children: [msg.text] }
                              ]
                            }))
                          : [{
                              tag: 'div',
                              attrs: { class: 'chat-message', style: 'opacity: 0.6; font-style: italic;' },
                              children: [
                                { tag: 'p', children: ['No messages yet. Start the conversation!'] }
                              ]
                            }]
                      }
                    ]
                  },
                  {
                    tag: 'div',
                    attrs: { class: 'input-form' },
                    children: [
                      {
                        tag: 'form',
                        attrs: { action: '#', method: 'post' },
                        children: [
                          {
                            tag: 'input',
                            attrs: {
                              type: 'text',
                              placeholder: 'Message',
                              class: 'chat-input'
                            }
                          },
                          {
                            tag: 'button',
                            attrs: { type: 'submit', class: 'send-button' },
                            children: [
                              {
                                tag: 'img',
                                attrs: {
                                  src: './static/images/send.png',
                                  alt: 'Send Icon'
                                }
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          },

      ],
    };
    renderDOM(vdom, root);
    setupChatToggle();
    setupChatInput();
    setupMainMenuButton();

  function setupChatToggle() {
    const chatDrawer = document.querySelector('.chat-drawer');
    const chatContainer = document.querySelector('.chat-container');
    if (chatDrawer && chatContainer) {
      chatDrawer.addEventListener('click', () => {
        chatContainer.classList.toggle('closed');
      });
    }
  }



  function setupChatInput() {
    const chatForm = document.querySelector('.input-form form');
    const chatInput = document.querySelector('.chat-input');

    if (chatForm && chatInput) {
      chatForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const message = chatInput.value.trim();
        if (message) {
          sendChat(message);
          chatInput.value = '';
        }
      });
    }
  }

  function setupMainMenuButton() {
    const mainMenuBtn = document.querySelector('a[href="/"]');
    if (mainMenuBtn) {
      mainMenuBtn.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('[LobbyScreen] Back to Main Menu clicked - closing WebSocket connection');
        
        // Close WebSocket connection before navigating
        closeConnection();
        
        // Navigate to main menu
        navigate('/');
      });
    }
  }
  }

  // Listen for state updates from server
  subscribeToState((state) => {
    const playerListStr = (state.players || []).map(p => p.id).join(',');
    const count = (state.players || []).length;
    const phase = state.phase;
    const cd = state.lobbyCountdown || 0;
    const wt = state.waitingTime || 0;
    const chatStr = JSON.stringify(state.chat || []);

    // Debug logging for phase transitions
    if (phase !== lastPhase) {
      console.log('[LobbyScreen] Phase transition:', lastPhase, '->', phase);
    }

    // Debug logging for player count changes
    if (count !== lastCount) {
      console.log('[LobbyScreen] Player count changed:', lastCount, '->', count);
      if (count < lastCount) {
        console.log('[LobbyScreen] Player(s) left, updating lobby display');
      }
    }

    // Check if we need to update timer display only
    const timerChanged = (cd !== lastCountdown || wt !== lastWaitingTime);
    const otherDataChanged = (playerListStr !== lastPlayerList || count !== lastCount || phase !== lastPhase);

    // Update chat messages separately to avoid re-rendering the form
    if (chatStr !== lastChatStr) {
      updateChatMessages(state.chat || []);
      lastChatStr = chatStr;
    }

    // Only update timer display without full re-render
    if (timerChanged && !otherDataChanged) {
      updateTimerDisplay(cd, wt);
      lastCountdown = cd;
      lastWaitingTime = wt;
      return;
    }

    // Only skip re-render if all relevant data is unchanged
    if (playerListStr === lastPlayerList && count === lastCount && phase === lastPhase && cd === lastCountdown && wt === lastWaitingTime) {
      return;
    }

    lastPlayerList = playerListStr;
    lastCount = count;
    lastPhase = phase;
    lastCountdown = cd;
    lastWaitingTime = wt;

    // Only navigate to game when server changes phase to 'game' and we have complete game state
    if (state.phase === 'game' && Array.isArray(state.map) && Array.isArray(state.players) && state.players.length > 0 && !started) {
      console.log('[LobbyScreen] Starting game, navigating to /game');
      started = true;
      if (window.location.pathname !== '/game') {
        navigate('/game');
      }
      return;
    }

    // Always render the lobby when in lobby phase
    if (state.phase === 'lobby') {
      console.log('[LobbyScreen] Rendering lobby with', count, 'players');
      renderLobby(state.players || [], count, cd, wt, state.chat || []);
    }
  });

  // Initial render
  const state = getState();
  renderLobby(state.players || [], (state.players || []).length, state.lobbyCountdown || 0, state.waitingTime || 0, state.chat || []);
  
  // Initialize timer display after first render
  setTimeout(() => {
    updateTimerDisplay(state.lobbyCountdown || 0, state.waitingTime || 0);
  }, 100);

  // Periodic state check to ensure synchronization
  setInterval(() => {
    const currentState = getState();
    if (currentState && currentState.players) {
      const currentCount = currentState.players.length;
      if (currentCount !== lastCount) {
        console.log('[LobbyScreen] Periodic check: Player count mismatch detected, forcing update');
        // Force a re-render if player count doesn't match
        lastCount = -1; // Force update on next state change
      }
    }
  }, 2000); // Check every 2 seconds
}
