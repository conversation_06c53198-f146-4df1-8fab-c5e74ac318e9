
# 💣 bomberman-dom

Multiplayer Bomberman Clone – A fast-paced, browser-based battle game where 2–4 players compete to be the last one standing. Built entirely using a custom JavaScript mini-framework, it’s designed for developers learning real-time multiplayer mechanics, performance optimization, and WebSocket-based communication. Perfect for those who want hands-on experience creating games without relying on Canvas, WebGL, or external game engines.

## ✨ Features

- 🎮 **Multiplayer Battles** – 2–4 players face off in real time.  
- 🛠 **Custom Framework** – No Canvas, WebGL, or external engines.  
- ⚡ **Smooth Performance** – Maintains 60 FPS with optimized rendering.  
- 🗺 **Randomized Maps** – Destructible blocks spawn differently each match.  
- 💥 **Power-Ups** – Extra bombs, extended explosion range, or faster movement.  
- 🛡 **Safe Spawn Zones** – Each player starts with space to escape.  
- 💬 **Real-Time Chat** – Communicate via WebSockets.  
- ⏳ **Match Countdown** – Automatic start when enough players join.  
## 🕹 Gameplay Overview

- Players start in **opposite corners** of the map.  
- Destroy blocks to open paths and reveal **power-ups**.  
- Place bombs strategically to eliminate other players.  
- Avoid getting caught in **explosions**—you only have **3 lives**.  
- Be the **last one standing** to win! 
## 🎯 Controls
- Action	Key(s)
- Move Up	⬆ / W
- Move Down	⬇ / S
- Move Left	⬅ / A
- Move Right	➡ / D
- Place Bomb	Spacebar
## Usage / Examples

To run the game locally:

```bash
# Start the server and launch the game
./start.sh
```



## 📚 Framework Documentation

[Documentation](https://learn.reboot01.com/git/mohani/mini-framework/src/branch/main/README.md)


## 👨‍💻 Authors

- [@masagheer](https://learn.reboot01.com/git/masagheer)
- [@dalhayki](https://learn.reboot01.com/git/dalhayki)
- [@falsayya](https://learn.reboot01.com/git/falsayya)
- [@mohani](https://learn.reboot01.com/git/mohani)

