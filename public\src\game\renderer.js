// renderer.js
// DOM-based game rendering logic for Bomberman with 60fps performance
// Cross-browser compatible implementation

const TILE_SIZE = 32;
const MAP_WIDTH = 17;
const MAP_HEIGHT = 15;

// Cross-browser compatibility checks and polyfills
const hasRequestAnimationFrame = typeof requestAnimationFrame !== 'undefined';
const hasPerformanceNow = typeof performance !== 'undefined' && typeof performance.now === 'function';

// Fallback for older browsers
if (!hasRequestAnimationFrame) {
  window.requestAnimationFrame = function(callback) {
    return setTimeout(callback, 16.67); // ~60fps
  };
}

if (!hasPerformanceNow) {
  performance = { now: () => Date.now() };
}

// Performance tracking
let frameCount = 0;
let lastFpsUpdate = 0;
let currentFps = 0;

// Enhanced caching system for optimal performance
let gameBoard = null;
let tileElements = new Map();
let gameObjectElements = new Map();
let activeGameObjects = new Set(); // Track active objects for efficient cleanup
let lastMapHash = null; // Cache map state to avoid unnecessary re-renders
let elementPool = {
  players: [],
  bombs: [],
  explosions: [],
  powerups: []
}; // Object pooling for reuse

// Movement animation system
let playerPositions = new Map(); // Track player positions for smooth movement
let animationDuration = 200; // Movement animation duration in ms

// Smooth movement animation function
function animatePlayerMovement(element, positionData, duration) {
  const startX = positionData.x;
  const startY = positionData.y;
  const targetX = positionData.targetX;
  const targetY = positionData.targetY;

  const startTime = performance.now();

  function animate(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function for smooth movement (ease-out)
    const easeProgress = 1 - Math.pow(1 - progress, 3);

    // Calculate current position
    const currentX = startX + (targetX - startX) * easeProgress;
    const currentY = startY + (targetY - startY) * easeProgress;

    // Update element position
    element.style.left = `${currentX + 12}px`;
    element.style.top = `${currentY + 12}px`;

    // Update position tracking
    positionData.x = currentX;
    positionData.y = currentY;

    // Continue animation if not finished
    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      // Animation complete - ensure exact final position
      positionData.x = targetX;
      positionData.y = targetY;
      element.style.left = `${targetX + 12}px`;
      element.style.top = `${targetY + 12}px`;
    }
  }

  requestAnimationFrame(animate);
}

// Main render function optimized for 60fps DOM rendering
export function renderGame(state, container) {
  if (!container || !state) {
    return;
  }

  // Debug logging for state validation
  if (state.phase === 'game') {
  }

  // Performance measurement
  const now = performance.now();
  frameCount++;
  if (now - lastFpsUpdate >= 1000) {
    currentFps = Math.round((frameCount * 1000) / (now - lastFpsUpdate));
    frameCount = 0;
    lastFpsUpdate = now;
  }

  // Create or get game board
  if (!gameBoard || !container.contains(gameBoard)) {
    gameBoard = createGameBoard(container);
  }

  const map = state.map || [];
  if (map.length === 0) {
    // No map data, show loading
    showLoadingState(gameBoard);
    return;
  }

  // Render all game elements
  renderMapDOM(gameBoard, map);
  renderGameObjectsDOM(gameBoard, state);

  // Render FPS counter for performance monitoring
  renderFpsCounterDOM(gameBoard, currentFps);
}

// Create the main game board container
function createGameBoard(container) {
  // Clear container efficiently
  while (container.firstChild) {
    container.removeChild(container.firstChild);
  }

  const board = document.createElement('div');
  board.className = 'game-board';
  board.style.cssText = `
    position: relative;
    width: ${MAP_WIDTH * TILE_SIZE}px;
    height: ${MAP_HEIGHT * TILE_SIZE}px;
    margin: 0 auto;
    border: 2px solid #666;
    background: #90EE90;
    overflow: hidden;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    contain: layout style paint;
  `;

  container.appendChild(board);

  // Reset caches when creating new board
  resetCaches();

  return board;
}

// Efficient cache management
function resetCaches() {
  // Return pooled elements before clearing
  for (const element of gameObjectElements.values()) {
    returnElementToPool(element);
  }

  tileElements.clear();
  gameObjectElements.clear();
  activeGameObjects.clear();
  lastMapHash = null;
}

// Show loading state
function showLoadingState(board) {
  board.innerHTML = `
    <div style="
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 20px;
      font-family: Arial, sans-serif;
      text-align: center;
      background: rgba(0, 0, 0, 0.8);
      padding: 20px;
      border-radius: 8px;
    ">
      Loading game...
    </div>
  `;
}

// Optimized DOM-based map rendering with caching
function renderMapDOM(board, map) {
  // Create hash of map state to detect changes
  const mapHash = JSON.stringify(map);
  if (mapHash === lastMapHash && tileElements.size > 0) {
    return; // No changes, skip re-render
  }
  lastMapHash = mapHash;

  // Use document fragment for batch DOM operations
  const fragment = document.createDocumentFragment();
  const tilesToUpdate = [];

  for (let y = 0; y < map.length; y++) {
    for (let x = 0; x < map[y].length; x++) {
      const tile = map[y][x];
      const tileKey = `tile-${x}-${y}`;

      let tileElement = tileElements.get(tileKey);
      if (!tileElement) {
        tileElement = document.createElement('div');
        tileElement.className = 'game-tile';
        // Use CSS classes instead of inline styles for better performance
        tileElement.style.cssText = `
          position: absolute;
          left: ${x * TILE_SIZE}px;
          top: ${y * TILE_SIZE}px;
          width: ${TILE_SIZE}px;
          height: ${TILE_SIZE}px;
        `;
        fragment.appendChild(tileElement);
        tileElements.set(tileKey, tileElement);
      }

      // Batch style updates
      tilesToUpdate.push({ element: tileElement, tile });
    }
  }

  // Append all new tiles at once
  if (fragment.children.length > 0) {
    board.appendChild(fragment);
  }

  // Batch update tile appearances
  requestAnimationFrame(() => {
    tilesToUpdate.forEach(({ element, tile }) => {
      let className = 'game-tile';
      switch (tile) {
        case 'E': case 0: className += ' tile-empty'; break;
        case 'W': case 1: className += ' tile-wall'; break;
        case 'B': case 2: className += ' tile-destructible'; break;
        default: className += ' tile-empty'; break;
      }
      if (element.className !== className) {
        element.className = className;
      }
    });
  });
}

// Optimized game objects rendering with object pooling
function renderGameObjectsDOM(board, state) {
  const currentObjects = new Set();

  // Render powerups first (lowest layer)
  if (state.powerups && Array.isArray(state.powerups)) {
    state.powerups.forEach((powerup) => {
      if (!powerup.collected) {
        const objectId = `powerup-${powerup.id || powerup.row + '-' + powerup.col}`;
        currentObjects.add(objectId);
        renderPowerupDOM(board, powerup, objectId);
      }
    });
  }

  // Render bombs
  if (state.bombs && Array.isArray(state.bombs)) {
    state.bombs.forEach((bomb) => {
      if (!bomb.exploded) {
        const objectId = `bomb-${bomb.id || bomb.row + '-' + bomb.col}`;
        currentObjects.add(objectId);
        renderBombDOM(board, bomb, objectId);
      }
    });
  }

  // Render explosions
  if (state.explosions && Array.isArray(state.explosions)) {
    state.explosions.forEach((explosion) => {
      const objectId = `explosion-${explosion.id || explosion.row + '-' + explosion.col}`;
      currentObjects.add(objectId);
      renderExplosionDOM(board, explosion, objectId);
    });
  }

  // Render players (highest layer)
  if (state.players && Array.isArray(state.players)) {
    state.players.forEach((player, index) => {
      if (player && player.alive && typeof player.row === 'number' && typeof player.col === 'number') {
        const objectId = `player-${player.id || player.nickname || index}`;
        currentObjects.add(objectId);
        renderPlayerDOM(board, player, objectId);
      }
    });
  }

  // Efficiently remove objects that are no longer active
  cleanupInactiveObjects(currentObjects);
  activeGameObjects = currentObjects;
}

// Efficient cleanup of inactive objects
function cleanupInactiveObjects(currentObjects) {
  for (const objectId of activeGameObjects) {
    if (!currentObjects.has(objectId)) {
      const element = gameObjectElements.get(objectId);
      if (element && element.parentNode) {
        element.parentNode.removeChild(element);
        returnElementToPool(element);
      }
      gameObjectElements.delete(objectId);

      // Clean up position tracking for removed players
      if (objectId.startsWith('player-')) {
        playerPositions.delete(objectId);
      }
    }
  }
}

// Object pooling functions for performance
function getElementFromPool(type) {
  const pool = elementPool[type];
  if (pool && pool.length > 0) {
    return pool.pop();
  }
  return null;
}

function returnElementToPool(element) {
  if (!element) return;

  // Reset element state
  element.className = '';
  element.style.cssText = '';
  element.textContent = '';

  // Determine type and return to appropriate pool
  const type = element.dataset.poolType;
  if (type && elementPool[type]) {
    elementPool[type].push(element);
  }
}

function createOptimizedElement(type, className) {
  let element = getElementFromPool(type);
  if (!element) {
    element = document.createElement('div');
    element.dataset.poolType = type;
  }
  element.className = className;
  return element;
}

// Optimized DOM-based player rendering with smooth movement
function renderPlayerDOM(board, player, objectId) {
  if (!player || typeof player.row !== 'number' || typeof player.col !== 'number') {
    return;
  }

  const targetPixelX = player.col * TILE_SIZE;
  const targetPixelY = player.row * TILE_SIZE;

  // Get or create player element with caching
  let playerElement = gameObjectElements.get(objectId);
  if (!playerElement) {
    playerElement = createOptimizedElement('players', 'game-object player');
    gameObjectElements.set(objectId, playerElement);
    board.appendChild(playerElement);

    // Initialize position tracking
    playerPositions.set(objectId, {
      x: targetPixelX,
      y: targetPixelY,
      targetX: targetPixelX,
      targetY: targetPixelY
    });
  }

  // Get current position tracking
  let positionData = playerPositions.get(objectId);
  if (!positionData) {
    positionData = {
      x: targetPixelX,
      y: targetPixelY,
      targetX: targetPixelX,
      targetY: targetPixelY
    };
    playerPositions.set(objectId, positionData);
  }

  // Check if player has moved to a new target position
  if (positionData.targetX !== targetPixelX || positionData.targetY !== targetPixelY) {
    // Player moved - start smooth animation
    positionData.targetX = targetPixelX;
    positionData.targetY = targetPixelY;

    // Calculate animation duration based on player speed (faster players animate quicker)
    const speedMultiplier = player.speed || 1;
    const currentAnimationDuration = Math.max(100, animationDuration / speedMultiplier);

    // Animate to new position
    animatePlayerMovement(playerElement, positionData, currentAnimationDuration);
  }

  // Player colors based on ID or index
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
  const colorIndex = player.id ? player.id.charCodeAt(0) % colors.length : 0;

  // Update style (position will be handled by animation)
  const baseStyle = `
    position: absolute;
    width: ${TILE_SIZE - 8}px;
    height: ${TILE_SIZE - 8}px;
    background-color: ${colors[colorIndex]};
    border: 2px solid #000;
    box-sizing: border-box;
    z-index: 100;
    transition: none;
  `;

  // Only update style if it changed (excluding position)
  if (!playerElement.style.cssText.includes(colors[colorIndex])) {
    playerElement.style.cssText = baseStyle;
    // Set initial position
    playerElement.style.left = `${positionData.x + 12}px`;
    playerElement.style.top = `${positionData.y + 12}px`;
  }

  // Handle nickname efficiently - position it relative to current player position
  const nicknameId = `${objectId}-nickname`;
  let nicknameElement = gameObjectElements.get(nicknameId);

  if (player.nickname) {
    if (!nicknameElement) {
      nicknameElement = createOptimizedElement('players', 'player-nickname');
      gameObjectElements.set(nicknameId, nicknameElement);
      board.appendChild(nicknameElement);
    }

    if (nicknameElement.textContent !== player.nickname) {
      nicknameElement.textContent = player.nickname;
    }

    // Position nickname relative to current animated position
    const currentX = positionData.x;
    const currentY = positionData.y;

    const nicknameStyle = `
      position: absolute;
      left: ${currentX + TILE_SIZE / 2}px;
      top: ${currentY - 8}px;
      transform: translateX(-50%);
      color: #000;
      font-size: 12px;
      font-family: Arial, sans-serif;
      font-weight: bold;
      text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.8);
      z-index: 101;
      pointer-events: none;
      transition: left 0.2s ease-out, top 0.2s ease-out;
    `;

    if (nicknameElement.style.cssText !== nicknameStyle) {
      nicknameElement.style.cssText = nicknameStyle;
    }
  } else if (nicknameElement) {
    // Remove nickname if no longer needed
    nicknameElement.parentNode.removeChild(nicknameElement);
    gameObjectElements.delete(nicknameId);
    returnElementToPool(nicknameElement);
  }
}

// Optimized DOM-based bomb rendering
function renderBombDOM(board, bomb, objectId) {
  if (!bomb || typeof bomb.row !== 'number' || typeof bomb.col !== 'number') {
    return;
  }

  const pixelX = bomb.col * TILE_SIZE;
  const pixelY = bomb.row * TILE_SIZE;

  // Get or create bomb element with caching
  let bombElement = gameObjectElements.get(objectId);
  if (!bombElement) {
    bombElement = createOptimizedElement('bombs', 'game-object bomb');
    bombElement.textContent = '💣';
    gameObjectElements.set(objectId, bombElement);
    board.appendChild(bombElement);
  }

  // Update position efficiently
  const newStyle = `
    position: absolute;
    left: ${pixelX + 6}px;
    top: ${pixelY + 6}px;
    width: ${TILE_SIZE - 12}px;
    height: ${TILE_SIZE - 12}px;
    background-color: #ff0000;
    border: 2px solid #800;
    box-sizing: border-box;
    z-index: 50;
    animation: bomb-pulse 0.4s ease-in-out infinite alternate;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
  `;

  if (bombElement.style.cssText !== newStyle) {
    bombElement.style.cssText = newStyle;
  }
}

// Optimized DOM-based explosion rendering
function renderExplosionDOM(board, explosion, objectId) {
  if (!explosion || typeof explosion.row !== 'number' || typeof explosion.col !== 'number') {
    return;
  }

  const pixelX = explosion.col * TILE_SIZE;
  const pixelY = explosion.row * TILE_SIZE;

  // Get or create explosion container
  let explosionContainer = gameObjectElements.get(objectId);
  if (!explosionContainer) {
    explosionContainer = createOptimizedElement('explosions', 'game-object explosion-container');
    explosionContainer.innerHTML = `
      <div class="explosion-outer"></div>
      <div class="explosion-inner"></div>
      <div class="explosion-center"></div>
    `;
    gameObjectElements.set(objectId, explosionContainer);
    board.appendChild(explosionContainer);
  }

  // Update container position
  explosionContainer.style.cssText = `
    position: absolute;
    left: ${pixelX}px;
    top: ${pixelY}px;
    width: ${TILE_SIZE}px;
    height: ${TILE_SIZE}px;
    z-index: 75;
  `;
}

// Optimized DOM-based powerup rendering
function renderPowerupDOM(board, powerup, objectId) {
  if (!powerup || typeof powerup.row !== 'number' || typeof powerup.col !== 'number') {
    return;
  }

  const pixelX = powerup.col * TILE_SIZE;
  const pixelY = powerup.row * TILE_SIZE;

  // Get or create powerup element with caching
  let powerupElement = gameObjectElements.get(objectId);
  if (!powerupElement) {
    powerupElement = createOptimizedElement('powerups', 'game-object powerup');
    gameObjectElements.set(objectId, powerupElement);
    board.appendChild(powerupElement);
  }

  // Powerup colors and symbols based on type
  let color = '#FFD700';
  let symbol = '?';

  switch (powerup.type) {
    case 'speed_up':
      color = '#00FF00';
      symbol = 'S';
      break;
    case 'bomb_up':
      color = '#FF0000';
      symbol = 'B';
      break;
    case 'flame_up':
      color = '#0000FF';
      symbol = 'F';
      break;
  }

  // Update content and style efficiently
  if (powerupElement.textContent !== symbol) {
    powerupElement.textContent = symbol;
  }

  const newStyle = `
    position: absolute;
    left: ${pixelX + 8}px;
    top: ${pixelY + 8}px;
    width: ${TILE_SIZE - 16}px;
    height: ${TILE_SIZE - 16}px;
    background-color: ${color};
    border: 2px solid #000;
    box-sizing: border-box;
    z-index: 25;
    animation: powerup-pulse 1s ease-in-out infinite alternate;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    color: #fff;
    text-shadow: 1px 1px 1px #000;
    box-shadow: 0 0 10px ${color};
  `;

  if (powerupElement.style.cssText !== newStyle) {
    powerupElement.style.cssText = newStyle;
  }
}

// Optimized FPS counter with performance metrics
function renderFpsCounterDOM(board, fps) {
  const fpsCounterId = 'fps-counter';
  let fpsCounter = gameObjectElements.get(fpsCounterId);

  if (!fpsCounter) {
    fpsCounter = document.createElement('div');
    fpsCounter.className = 'fps-counter';
    fpsCounter.style.cssText = `
      position: absolute;
      top: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.8);
      padding: 6px 12px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      border-radius: 4px;
      z-index: 200;
      pointer-events: none;
      user-select: none;
      border: 1px solid rgba(255, 255, 255, 0.2);
    `;
    board.appendChild(fpsCounter);
    gameObjectElements.set(fpsCounterId, fpsCounter);
  }

  // Update content and color efficiently
  const newText = `FPS: ${fps}`;
  const newColor = fps >= 55 ? '#0f0' : fps >= 30 ? '#ff0' : '#f00';

  if (fpsCounter.textContent !== newText) {
    fpsCounter.textContent = newText;
  }

  if (fpsCounter.style.color !== newColor) {
    fpsCounter.style.color = newColor;
  }
}

// Export renderGameDOM as an alias to renderGame for backward compatibility
export const renderGameDOM = renderGame;
