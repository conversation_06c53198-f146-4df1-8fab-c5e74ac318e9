// main.js
import { defineRoute, renderRoute, setDefaultRoute } from '../msec/framework/router.js';
import { nicknameScreen } from './ui/nicknameScreen.js';
import { lobbyScreen } from './ui/lobbyScreen.js';
import { gameScreen } from './ui/gameScreen.js';
import { GameDashboardScreen } from './ui/GameDashboardScreen.js';
import { setState } from '../msec/framework/state.js';
import { StartScreen } from './ui/startScreen.js';

// Don't connect to WebSocket automatically - let each screen handle its own connection
// This prevents connection issues when starting new rounds
console.log('[Main] WebSocket connection will be established when entering nickname screen');

// Define routes
defineRoute('/', StartScreen);
defineRoute('/player', nicknameScreen);
defineRoute('/lobby', lobbyScreen);
defineRoute('/game', GameDashboardScreen);
// defineRoute('/game', gameScreen);

// Set default route for unmatched URLs
setDefaultRoute('/');

// Initial render
window.onload = () => {
  // Ensure there is a root element
  if (!document.getElementById('app')) {
    const appDiv = document.createElement('div');
    appDiv.id = 'app';
    document.body.appendChild(appDiv);
  }
  renderRoute();
};
