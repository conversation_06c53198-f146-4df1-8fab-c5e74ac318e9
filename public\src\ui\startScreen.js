// StartScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { navigate } from '../../msec/framework/router.js';
import { connect } from '../multiplayer/socket.js';

export function StartScreen() {
  const root = document.getElementById('app');
  
  console.log('[StartScreen] Rendering main start screen');

  // Don't automatically connect to WebSocket here - let nickname screen handle fresh connections
  console.log('[StartScreen] Main menu - WebSocket connection will be established when entering nickname');

  const vdom = {
    tag: 'div',
    attrs: { 
      class: 'start-screen',
      style: `
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #07131F 0%, #1a1a2e 100%);
        color: white;
        font-family: 'EngraversGothic', Arial, sans-serif;
        padding: 20px;
      `
    },
    children: [
      {
        tag: 'h1',
        attrs: { 
          style: `
            font-size: 4rem;
            margin-bottom: 50px;
            text-align: center;
            background: linear-gradient(45deg, #4ECDC4, #FF6B6B);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            letter-spacing: 8px;
          `
        },
        children: ['BOMBERMAN']
      },
      {
        tag: 'div',
        attrs: { 
          class: 'start-buttons',
          style: `
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
          `
        },
        children: [
          {
            tag: 'button',
            attrs: { 
              id: 'start-game-btn',
              style: `
                background: linear-gradient(45deg, #4ECDC4, #45B7D1);
                color: white;
                border: none;
                padding: 20px 40px;
                border-radius: 15px;
                font-size: 1.5rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                letter-spacing: 2px;
                text-transform: uppercase;
                font-family: 'EngraversGothic', Arial, sans-serif;
                min-width: 200px;
              `
            },
            children: ['START GAME']
          },
          {
            tag: 'button',
            attrs: { 
              id: 'exit-btn',
              style: `
                background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
                color: white;
                border: none;
                padding: 20px 40px;
                border-radius: 15px;
                font-size: 1.5rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                letter-spacing: 2px;
                text-transform: uppercase;
                font-family: 'EngraversGothic', Arial, sans-serif;
                min-width: 200px;
              `
            },
            children: ['EXIT']
          }
        ]
      }
    ]
  };

  renderDOM(vdom, root);

  // Add event listeners
  const startGameBtn = document.getElementById('start-game-btn');
  const exitBtn = document.getElementById('exit-btn');

  if (startGameBtn) {
    startGameBtn.addEventListener('click', () => {
      console.log('[StartScreen] Start Game button clicked');
      navigate('/player');
    });

    startGameBtn.addEventListener('mouseenter', () => {
      startGameBtn.style.transform = 'translateY(-3px)';
      startGameBtn.style.boxShadow = '0 8px 25px rgba(78, 205, 196, 0.4)';
    });

    startGameBtn.addEventListener('mouseleave', () => {
      startGameBtn.style.transform = 'translateY(0)';
      startGameBtn.style.boxShadow = 'none';
    });
  }

  if (exitBtn) {
    exitBtn.addEventListener('click', () => {
      console.log('[StartScreen] Exit button clicked');
      // Close the window/tab
      window.close();
      // Fallback for browsers that don't allow window.close()
      if (!window.closed) {
        alert('Please close this tab manually to exit the game.');
      }
    });

    exitBtn.addEventListener('mouseenter', () => {
      exitBtn.style.transform = 'translateY(-3px)';
      exitBtn.style.boxShadow = '0 8px 25px rgba(255, 107, 107, 0.4)';
    });

    exitBtn.addEventListener('mouseleave', () => {
      exitBtn.style.transform = 'translateY(0)';
      exitBtn.style.boxShadow = 'none';
    });
  }
}

