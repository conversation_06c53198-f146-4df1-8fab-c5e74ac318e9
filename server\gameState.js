// server/gameState.js

const { v4: uuidv4 } = require('uuid');

const MAP_WIDTH = 17;
const MAP_HEIGHT = 15;
const TILE_SIZE = 32;
const SPAWN_POSITIONS = [
  { row: 0, col: 0 },
  { row: 0, col: MAP_WIDTH - 1 },
  { row: MAP_HEIGHT - 1, col: 0 },
  { row: MAP_HEIGHT - 1, col: MAP_WIDTH - 1 },
];

const POWERUP_TYPES = ['bomb_up', 'flame_up', 'speed_up'];

function randomPowerupType() {
  return POWERUP_TYPES[Math.floor(Math.random() * POWERUP_TYPES.length)];
}

function generateMap() {
  // Similar to client
  const map = [];
  for (let row = 0; row < MAP_HEIGHT; row++) {
    const currentRow = [];
    for (let col = 0; col < MAP_WIDTH; col++) {
      // Always empty for spawn corners and their adjacent tiles
      if (
        (row < 2 && col < 2) ||
        (row < 2 && col > MAP_WIDTH - 3) ||
        (row > MAP_HEIGHT - 3 && col < 2) ||
        (row > MAP_HEIGHT - 3 && col > MAP_WIDTH - 3) ||
        (row === 2 && (col === 0 || col === MAP_WIDTH - 1)) ||
        (row === 0 && (col === 2 || col === MAP_WIDTH - 3)) ||
        (row === MAP_HEIGHT - 3 && (col === 0 || col === MAP_WIDTH - 1)) ||
        (row === MAP_HEIGHT - 1 && (col === 2 || col === MAP_WIDTH - 3))
      ) {
        currentRow.push('E');
        continue;
      }
      if (row % 2 === 1 && col % 2 === 1) {
        currentRow.push('W');
        continue;
      }
      currentRow.push(Math.random() < 0.6 ? 'B' : 'E');
    }
    map.push(currentRow);
  }
  return map;
}

class Player {
  constructor(id, nickname, startRow, startCol) {
    this.id = id;
    this.nickname = nickname;
    this.row = startRow;
    this.col = startCol;
    this.lives = 3;
    this.speed = 1;
    this.bombsCount = 1;
    this.flameRange = 1;
    this.alive = true;
    this.lastMoveTime = 0; // Track last movement time for speed control
  }
}

class Bomb {
  constructor(row, col, ownerId, flameRange) {
    this.row = row;
    this.col = col;
    this.ownerId = ownerId;
    this.flameRange = flameRange;
    this.timer = 3000;
    this.exploded = false;
    this.startTime = Date.now();
  }
}

class PowerUp {
  constructor(row, col, type) {
    this.row = row;
    this.col = col;
    this.type = type;
    this.collected = false;
  }
}

class GameState {
  constructor() {
    this.map = generateMap();
    this.players = [];
    this.bombs = [];
    this.explosions = [];
    this.powerups = [];
    this.chat = [];
    this.phase = 'lobby';
    this.winner = null;
    this.lastUpdate = Date.now();
    this.countdown = 10;
    this.lobbyStart = Date.now();
    this._lastPlayerCount = 0; // Initialize for tracking player count changes
  }

  addPlayer(nickname) {
    // removed console.log
    if (this.players.length >= 4) return null;
    // Find the first available spawn position
    const usedPositions = this.players.map(p => `${p.row},${p.col}`);
    // removed console.log
    let pos = null;
    for (const candidate of SPAWN_POSITIONS) {
      if (!usedPositions.includes(`${candidate.row},${candidate.col}`)) {
        pos = candidate;
        break;
      }
    }
    if (!pos) {
      // removed console.error
      return null;
    }
    const id = uuidv4();
    const player = new Player(id, nickname, pos.row, pos.col);
    this.players.push(player);
    // removed console.log
    // removed console.log
    return player;
  }

  removePlayer(id) {
    const player = this.players.find(p => p.id === id);
    // removed console.log
    this.players = this.players.filter(p => p.id !== id);
    // removed console.log
  }

  handleAction(id, action) {
    const player = this.players.find(p => p.id === id);
    // removed console.log
    if (!player) return;

    // Allow restart, lobby, and mainMenu actions regardless of game phase or player alive status
    if (action.type === 'restart' || action.type === 'lobby' || action.type === 'mainMenu') {
      // These actions are handled below, continue processing
    } else {
      // For all other actions, require player to be alive and game to be in 'game' phase
      if (!player.alive || this.phase !== 'game') return;
    }

    if (action.type === 'move') {
      // Handle movement with proper speed mechanics and timing
      const now = Date.now();

      // Calculate movement cooldown based on speed (higher speed = shorter cooldown)
      // Base cooldown is 300ms, reduced by speed: 300ms / speed
      const movementCooldown = Math.max(100, 300 / player.speed); // Min 100ms cooldown

      // Check if enough time has passed since last movement
      if (now - player.lastMoveTime < movementCooldown) {
        return; // Movement too fast, ignore
      }

      let { row, col } = player;
      let newRow = row;
      let newCol = col;

      switch (action.dir) {
        case 'up': newRow = row - 1; break;
        case 'down': newRow = row + 1; break;
        case 'left': newCol = col - 1; break;
        case 'right': newCol = col + 1; break;
      }

      // Check boundaries
      if (newRow < 0 || newRow >= MAP_HEIGHT || newCol < 0 || newCol >= MAP_WIDTH) return;

      // Check walkable
      if (this.map[newRow][newCol] !== 'E') return;

      // Prevent walking onto a bomb, except when moving away from your own bomb
      const bombAtTarget = this.bombs.find(b => !b.exploded && b.row === newRow && b.col === newCol);
      if (bombAtTarget) {
        // Allow movement if:
        // 1. It's your own bomb and you're currently on the same tile (moving away)
        // 2. You're moving within the same tile (shouldn't happen in discrete system, but safe)
        const isOwnBombAndMovingAway = (bombAtTarget.ownerId === player.id &&
                                       player.row === newRow && player.col === newCol);

        if (!isOwnBombAndMovingAway) {
          // removed console.log
          return; // Block movement onto other bombs or onto bombs from outside
        } else {
          // removed console.log
        }
      }

      // Move is valid - update position and timestamp
      player.row = newRow;
      player.col = newCol;
      player.lastMoveTime = now;

      // Check for powerup pickup
      this.powerups.forEach(powerup => {
        if (!powerup.collected && powerup.row === newRow && powerup.col === newCol) {
          // removed console.log
          switch (powerup.type) {
            case 'bomb_up':
              player.bombsCount = Math.min(player.bombsCount + 1, 8); // Cap at 8 bombs
              break;
            case 'flame_up':
              const oldFlameRange = player.flameRange;
              player.flameRange = Math.min(player.flameRange + 1, 10); // Cap at 10 range
              // removed console.log
              break;
            case 'speed_up':
              player.speed = Math.min(player.speed + 1, 5); // Cap at 5 speed
              break;
          }
          powerup.collected = true;
        }
      });
    } else if (action.type === 'bomb') {
      const activeBombs = this.bombs.filter(b => b.ownerId === player.id && !b.exploded);
      if (activeBombs.length >= player.bombsCount) return;
      if (this.bombs.some(b => b.row === player.row && b.col === player.col && !b.exploded)) return;

      // removed console.log
      this.bombs.push(new Bomb(player.row, player.col, player.id, player.flameRange));
    } else if (action.type === 'restart') {
      // Only allow restart if game is ended and player was in the game
      // removed console.log
      if (this.phase === 'end') {
        // removed console.log
        this.restartGame();
      } else {
        // removed console.log
      }
    } else if (action.type === 'mainMenu') {
      // Handle mainMenu action - ALWAYS end the game for everyone
      console.log('[GameState] Player', id, 'requested main menu. Ending game for all players.');
      console.log('[GameState] Current phase before mainMenu:', this.phase);
      
      // Always end the game when someone clicks main menu
      this.phase = 'end';
      this.winner = 'Game ended by player';
      console.log('[GameState] Game phase changed to:', this.phase, 'Winner:', this.winner);
      
      // Don't remove any players yet - let them all see the end screen first
      console.log('[GameState] All players will see end screen before removal');
      return;
    } else if (action.type === 'lobby') {
      // Return to lobby - this should always work regardless of current phase
      // This allows players to return to lobby from any game state
      console.log('[GameState] Lobby action received, returning to lobby phase');
      this.returnToLobby();
    }
  }

  handleChat(id, text) {
    const player = this.players.find(p => p.id === id);
    if (!player) return;
    this.chat.push({ nickname: player.nickname, text });
    if (this.chat.length > 50) this.chat.shift();
  }

  update() {
    const now = Date.now();
    // Lobby logic
    if (this.phase === 'lobby') {
      // If all players left, reset lobby timer and clear chat for fresh session
      if (this.players.length === 0) {
        this.lobbyStart = now;
        this.countdown = 10;
        this._lastPlayerCount = 0;
        // Clear chat history when all players leave to start fresh session
        if (this.chat.length > 0) {
          this.chat = [];
        }
        return;
      }

      // Handle player count changes and reset timers appropriately
      if (this._lastPlayerCount !== undefined && this._lastPlayerCount !== this.players.length) {
        // If player count goes from 1 to 2, reset lobbyStart for 20s wait
        if (this._lastPlayerCount === 1 && this.players.length === 2) {
          this.lobbyStart = now;
          this.countdown = 10;
        }
        // If player count reaches 4, reset for immediate countdown
        else if (this.players.length === 4 && this._lastPlayerCount < 4) {
          this.lobbyStart = now;
          this.countdown = 10;
        }
        // If players leave during countdown/waiting, reset appropriately
        else if (this.players.length < this._lastPlayerCount) {
          // If we drop below 2 players, reset everything
          if (this.players.length < 2) {
            this.lobbyStart = now;
            this.countdown = 10;
          }
          // If we drop from 4 to 3 or fewer, reset to 20s wait + 10s countdown logic
          else if (this._lastPlayerCount === 4 && this.players.length < 4) {
            this.lobbyStart = now;
            this.countdown = 10;
          }
        }
      }
      this._lastPlayerCount = this.players.length;

      // Game start logic based on requirements:
      // - With 2-3 players: wait 20s, then 10s countdown
      // - With 4 players: immediate 10s countdown
      const timeSinceLobbyStart = now - this.lobbyStart;

      if (this.players.length >= 2) {
        if (this.players.length === 4) {
          // 4 players: immediate countdown
          this.countdown = Math.max(0, 10 - Math.floor(timeSinceLobbyStart / 1000));
          if (this.countdown === 0) {
            this.startGame();
          }
        } else {
          // 2-3 players: 20s wait, then 10s countdown
          if (timeSinceLobbyStart >= 20000) {
            // Start countdown after 20s
            this.countdown = Math.max(0, 10 - Math.floor((timeSinceLobbyStart - 20000) / 1000));
            if (this.countdown === 0) {
              this.startGame();
            }
          } else {
            // Still in 20s wait period
            this.countdown = 10;
          }
        }
      }
      return;
    }
    // Game logic
    // Update bombs
    this.bombs.forEach(bomb => {
      if (!bomb.exploded && now - bomb.startTime >= bomb.timer) {
        this.handleExplosion(bomb);
      }
    });
    // Remove expired explosions
    this.explosions = this.explosions.filter(ex => ex.expiresAt > now);

    // Win condition - check for game end (only if not already ended by mainMenu)
    if (this.phase !== 'end' || this.winner !== 'Game ended by player') {
      const alive = this.players.filter(p => p.alive);
      if (alive.length <= 1 && !this.winner && this.players.length > 1) {
        if (alive.length === 1) {
          this.winner = alive[0].nickname;
          // removed console.log
        } else {
          this.winner = 'Draw'; // All players died
        }
        this.phase = 'end';
        this.gameEndTime = now;
        console.log('[GameState] Game ended naturally - phase:', this.phase, 'winner:', this.winner);
      }
    }

    // Remove players marked for removal
    this.players = this.players.filter(p => !p.markedForRemoval);
  }

  handleExplosion(bomb) {
    if (bomb.exploded) return;

    // removed console.log
    bomb.exploded = true;

    const affected = [{ row: bomb.row, col: bomb.col }];
    const directions = [
      { dr: -1, dc: 0 },
      { dr: 1, dc: 0 },
      { dr: 0, dc: -1 },
      { dr: 0, dc: 1 },
    ];

    for (const { dr, dc } of directions) {
      for (let i = 1; i <= bomb.flameRange; i++) {
        const r = bomb.row + dr * i;
        const c = bomb.col + dc * i;
        if (r < 0 || r >= MAP_HEIGHT || c < 0 || c >= MAP_WIDTH) break;
        if (this.map[r][c] === 'W') break;
        affected.push({ row: r, col: c });
        if (this.map[r][c] === 'B') break;
      }
    }

    const now = Date.now();
    affected.forEach(({ row, col }) => {
      this.explosions.push({ row, col, expiresAt: now + 500 });
    });

    // Destroy blocks, maybe spawn powerups
    affected.forEach(({ row, col }) => {
      if (this.map[row][col] === 'B') {
        this.map[row][col] = 'E';
        if (Math.random() < 0.3) {
          this.powerups.push(new PowerUp(row, col, randomPowerupType()));
        }
      }
    });

    // Damage players
    this.players.forEach(player => {
      if (!player.alive) return;
      if (affected.some(a => a.row === player.row && a.col === player.col)) {
        player.lives--;
        // removed console.log
        if (player.lives <= 0) {
          player.alive = false;
          // removed console.log
        }
      }
    });

    // Chain reaction - find bombs that should explode and trigger them
    const bombsToExplode = this.bombs.filter(b =>
      !b.exploded && affected.some(a => a.row === b.row && a.col === b.col)
    );

    if (bombsToExplode.length > 0) {
      // removed console.log
      // Use setTimeout to prevent stack overflow with deep recursion
      bombsToExplode.forEach(b => {
        setTimeout(() => this.handleExplosion(b), 50); // Small delay for visual effect
      });
    }
  }

  startGame() {
    // removed console.log
    this.phase = 'game';
    this.lastUpdate = Date.now();
    this.map = generateMap();
    this.bombs = [];
    this.explosions = [];
    this.powerups = [];
    this.winner = null;
    this.gameEndTime = null;
    // Clear chat history when starting a new game for fresh session
    this.chat = [];

    // Reset player positions and lives with randomized spawn positions
    const positions = [...SPAWN_POSITIONS];
    // Shuffle the spawn positions for fresh player assignments each round
    for (let i = positions.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [positions[i], positions[j]] = [positions[j], positions[i]];
    }

    for (let i = 0; i < this.players.length; i++) {
      const p = this.players[i];
      const pos = positions[i % positions.length];
      p.row = pos.row;
      p.col = pos.col;
      p.lives = 3;
      p.speed = 1;
      p.bombsCount = 1;
      p.flameRange = 1;
      p.alive = true;
    }
  }

  restartGame() {
    // removed console.log
    // Always return to lobby for proper countdown sequence, regardless of player count
    this.returnToLobby();
  }

  returnToLobby() {
    this.phase = 'lobby';
    this.lobbyStart = Date.now();
    this.countdown = 10;
    this._lastPlayerCount = this.players.length; // Reset player count tracking
    this.winner = null;
    this.gameEndTime = null;
    this.map = [];
    this.bombs = [];
    this.explosions = [];
    this.powerups = [];
    // Clear chat history when returning to lobby for fresh session
    this.chat = [];

    // Keep players but reset their game state
    this.players.forEach(p => {
      p.lives = 3;
      p.speed = 1;
      p.bombsCount = 1;
      p.flameRange = 1;
      p.alive = true;
      // Reset player positions to spawn positions
      const spawnIndex = this.players.indexOf(p) % SPAWN_POSITIONS.length;
      const spawnPos = SPAWN_POSITIONS[spawnIndex];
      p.row = spawnPos.row;
      p.col = spawnPos.col;
    });
    
    console.log('[GameState] Returned to lobby with', this.players.length, 'players');
  }

  getState() {
    let lobbyCountdown = 0;
    let waitingTime = 0;

    if (this.phase === 'lobby') {
      const timeSinceLobbyStart = Date.now() - this.lobbyStart;

      if (this.players.length >= 2) {
        if (this.players.length === 4) {
          // 4 players: immediate countdown
          lobbyCountdown = this.countdown;
        } else {
          // 2-3 players: show waiting time or countdown
          const timeSinceLobbyStart = Date.now() - this.lobbyStart;
          if (timeSinceLobbyStart < 20000) {
            waitingTime = Math.max(0, 20 - Math.floor(timeSinceLobbyStart / 1000));
            lobbyCountdown = 0;
          } else {
            lobbyCountdown = this.countdown;
            waitingTime = 0;
          }
        }
      }
    }

    return {
      map: this.map,
      players: this.players,
      bombs: this.bombs,
      explosions: this.explosions,
      powerups: this.powerups,
      chat: this.chat,
      phase: this.phase,
      winner: this.winner,
      countdown: this.countdown,
      waitingTime,
    };
  }
}

module.exports = GameState;
