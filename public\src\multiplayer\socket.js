// socket.js
// Simple WebSocket client for Bomberman

let socket = null;
let listeners = [];
let playerId = localStorage.getItem('playerId') || null;
let messageQueue = []; // Queue for messages sent before socket is ready
let autoHandshakeEnabled = true; // Control auto-handshake behavior

export function connect(url) {
  // Close existing connection if any
  if (socket) {
    console.log('[Socket] Closing existing connection before creating new one');
    socket.close();
    socket = null;
  }

  console.log('[Socket] Creating new WebSocket connection to:', url);
  socket = new WebSocket(url);
  
  socket.onopen = () => {
    console.log('[Socket] WebSocket connection opened successfully');

    // Flush any queued messages
    if (messageQueue.length > 0) {
      console.log('[Socket] Flushing', messageQueue.length, 'queued messages');
      while (messageQueue.length > 0) {
        const queuedMsg = messageQueue.shift();
        socket.send(JSON.stringify(queuedMsg));
      }
    }

    // Auto-handshake: only if enabled and we have a valid session
    if (autoHandshakeEnabled && hasValidSession()) {
      const storedNickname = localStorage.getItem('nickname');
      console.log('[Socket] Auto-handshake with nickname:', storedNickname);
      send({ type: 'nickname', nickname: storedNickname });
    }
  };
  socket.onmessage = (event) => {
    const msg = JSON.parse(event.data);
    console.log('[Socket] Received message:', msg.type, msg);
    
    if (msg.type === 'playerId' && msg.id) {
      playerId = msg.id;
      localStorage.setItem('playerId', playerId);
    }
    
    // Special handling for state messages to debug phase changes
    if (msg.type === 'state' && msg.state) {
      console.log('[Socket] State update - Phase:', msg.state.phase, 'Winner:', msg.state.winner, 'Players:', msg.state.players?.length);
    }
    
    listeners.forEach(fn => fn(msg));
  };
  socket.onclose = () => {
    // Clear any queued messages on disconnect
    messageQueue = [];
    // Removed auto-reconnect to prevent flickering
    // setTimeout(() => connect(url), 2000);
  };

  socket.onerror = (error) => {
    console.error('[Socket] WebSocket error:', error);
  };
}

export function send(msg) {
  // Add playerId to nickname messages
  if (msg.type === 'nickname') {
    msg.playerId = playerId;
  }

  // Only send if socket is fully open, not connecting
  if (socket && socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify(msg));
  } else if (socket && socket.readyState === WebSocket.CONNECTING) {
    // If still connecting, queue the message
    console.log('[Socket] WebSocket still connecting, queuing message:', msg.type);
    messageQueue.push(msg);
  } else {
    // If no socket or closed, queue the message
    console.log('[Socket] No WebSocket connection, queuing message:', msg.type);
    messageQueue.push(msg);
  }
}

export function onMessage(fn) {
  listeners.push(fn);
}

export function isConnected() {
  return socket && socket.readyState === WebSocket.OPEN;
}

export function isConnecting() {
  return socket && socket.readyState === WebSocket.CONNECTING;
}

export function getConnectionStatus() {
  if (!socket) return 'disconnected';
  switch (socket.readyState) {
    case WebSocket.CONNECTING: return 'connecting';
    case WebSocket.OPEN: return 'connected';
    case WebSocket.CLOSING: return 'closing';
    case WebSocket.CLOSED: return 'closed';
    default: return 'unknown';
  }
}

export function getPlayerId() {
  return playerId;
}

// Utility function to clear stored session data (for testing/debugging)
export function clearSession() {
  localStorage.removeItem('playerId');
  localStorage.removeItem('nickname');
  localStorage.removeItem('sessionTimestamp');
  playerId = null;
  messageQueue = [];
}

// Check if we have a stored session
export function hasStoredSession() {
  return !!(localStorage.getItem('nickname') && localStorage.getItem('playerId'));
}

// Check if we have a valid session (not expired)
function hasValidSession() {
  const nickname = localStorage.getItem('nickname');
  const playerId = localStorage.getItem('playerId');
  const sessionTimestamp = localStorage.getItem('sessionTimestamp');

  if (!nickname || !playerId || !sessionTimestamp) {
    return false;
  }

  // Session expires after 1 hour
  const sessionAge = Date.now() - parseInt(sessionTimestamp);
  const SESSION_TIMEOUT = 60 * 60 * 1000; // 1 hour

  if (sessionAge > SESSION_TIMEOUT) {
    clearSession();
    return false;
  }

  return true;
}

// Disable auto-handshake (when user wants to enter new nickname)
export function disableAutoHandshake() {
  autoHandshakeEnabled = false;
}

// Enable auto-handshake (for normal reconnects)
export function enableAutoHandshake() {
  autoHandshakeEnabled = true;
}

// Start a new session (clear old data and disable auto-handshake)
export function startNewSession() {
  console.log('[Socket] Starting new session - clearing old data');
  clearSession();
  disableAutoHandshake();
  
  // Close existing socket connection to ensure clean state
  if (socket && socket.readyState === WebSocket.OPEN) {
    console.log('[Socket] Closing existing socket connection for new session');
    socket.close();
    socket = null;
  }
  
  // Clear any queued messages
  messageQueue = [];
}

// Close the WebSocket connection and clean up
export function closeConnection() {
  console.log('[Socket] Closing WebSocket connection');
  
  if (socket) {
    if (socket.readyState === WebSocket.OPEN) {
      socket.close();
    }
    socket = null;
  }
  
  // Clear session data
  clearSession();
  
  // Clear any queued messages
  messageQueue = [];
  
  console.log('[Socket] WebSocket connection closed and session cleared');
}
